import { app, BrowserWindow, shell, ipcMain, dialog, session, globalShortcut } from "electron";
import * as path from "path";
import * as os from "os";
import * as fs from "fs";
import { fileURLToPath } from "url";
import { initializeTusModule, type TusUploadConfig } from "./tus/index";
import { initializeDownloadModule, type StreamDownloadConfig } from "./stream-downloader/index";
import { initializeArchiveModule, type ArchiveConfig } from "./archive/index";
import { initializeExtractionModule, type ExtractionConfig } from "./7z-extractor/index";
import { initializeLogger, logger, type LoggerConfig } from "./logger/index";
import { initializeLoggerIPC } from "./logger/ipcHandlers";
import dotenv from "dotenv";

// 判断是否是开发环境
const isDev = !app.isPackaged;

// 开发环境下，路径是项目根目录
// 生产环境下，extraResources 会被放在 resources 目录下
const envPath = isDev ? path.resolve(app.getAppPath(), ".env.development") : path.resolve(process.resourcesPath, ".env.production");

// 根据是否打包选择环境文件
dotenv.config({ path: envPath });

// 获取当前文件的目录路径（ES module 方式）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 为 Windows 7 禁用 GPU 加速
if (os.release().startsWith("6.1")) {
  app.disableHardwareAcceleration();
}

// 为 Windows 10+ 通知设置应用程序名称
if (process.platform === "win32") {
  app.setAppUserModelId(app.getName());
}

if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}

let mainWindow: BrowserWindow | null = null;
let tusModuleCleanup: (() => void) | null = null;
let downloadModuleCleanup: (() => void) | null = null;
let archiveModuleCleanup: (() => void) | null = null;
let extractionModuleCleanup: (() => void) | null = null;

// 导入独立的认证模块
import { setAuthToken as setAuthTokenInStore, clearAuthToken } from "./auth/authStore";

const preload = path.join(__dirname, "preload.js");

/**
 * 设置拖拽文件处理
 */
function setupDragAndDropHandling(window: BrowserWindow) {
  // 监听拖拽进入事件
  window.webContents.on("dom-ready", () => {
    // 注入拖拽处理脚本到渲染进程
    window.webContents.executeJavaScript(`
      // 禁用默认的拖拽行为
      document.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.stopPropagation();
      });

      document.addEventListener('drop', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // 获取拖拽的文件路径
        const files = Array.from(e.dataTransfer.files);
        const filePaths = files.map(file => file.path).filter(Boolean);

        if (filePaths.length > 0) {
          // 通过IPC发送文件路径到主进程
          window.electronAPI.dragDrop.handleFileDrop(filePaths);
        }
      });
    `);
  });
}

/**
 * 注册全局快捷键以禁用刷新行为
 */
function registerDisableRefreshShortcuts() {
  // 禁用常见的刷新快捷键
  const refreshShortcuts = [
    "CmdOrCtrl+R", // Cmd+R (macOS) / Ctrl+R (Windows/Linux)
    "F5", // F5 刷新
  ];

  refreshShortcuts.forEach((shortcut) => {
    const success = globalShortcut.register(shortcut, () => {
      console.log(`🚫 已拦截刷新快捷键: ${shortcut}`);
      // 什么都不做，禁用刷新行为
    });

    if (success) {
      console.log(`✅ 成功注册快捷键拦截: ${shortcut}`);
    } else {
      console.log(`❌ 注册快捷键拦截失败: ${shortcut}`);
    }
  });
}

/**
 * 注册开发者工具快捷键
 */
function registerDevToolsShortcut() {
  const devToolsShortcut = "CmdOrCtrl+Shift+I"; // Cmd+Shift+I (macOS) / Ctrl+Shift+I (Windows/Linux)

  const success = globalShortcut.register(devToolsShortcut, () => {
    console.log(`🔧 开发者工具快捷键被触发: ${devToolsShortcut}`);

    // 获取当前聚焦的窗口
    const focusedWindow = BrowserWindow.getFocusedWindow();

    if (focusedWindow) {
      // 切换开发者工具的显示状态
      if (focusedWindow.webContents.isDevToolsOpened()) {
        focusedWindow.webContents.closeDevTools();
        console.log("🔧 已关闭开发者工具");
      } else {
        focusedWindow.webContents.openDevTools();
        console.log("🔧 已打开开发者工具");
      }
    } else {
      console.log("⚠️ 没有可用的窗口，忽略开发者工具快捷键操作");
    }
  });

  if (success) {
    console.log(`✅ 成功注册开发者工具快捷键: ${devToolsShortcut}`);
  } else {
    console.log(`❌ 注册开发者工具快捷键失败: ${devToolsShortcut}`);
  }
}

/**
 * 注销所有全局快捷键
 */
function unregisterAllShortcuts() {
  globalShortcut.unregisterAll();
  console.log("🧹 已注销所有全局快捷键");
}

async function createWindow() {
  // 配置会话安全设置
  const ses = session.defaultSession;

  // 设置内容安全策略和其他安全标头
  ses.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        "X-Content-Type-Options": ["nosniff"],
        "X-XSS-Protection": ["1; mode=block"],
        "Referrer-Policy": ["no-referrer"],
        "Permissions-Policy": ["camera=(), microphone=(), geolocation=()"],
      },
    });
  });

  mainWindow = new BrowserWindow({
    title: "Cloud Drive",
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false,
    autoHideMenuBar: true,
    webPreferences: {
      preload,
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
      sandbox: false,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      // devTools: isDev || isTest,
      devTools: true,
    },
  });

  // 设置窗口属性
  mainWindow.on("ready-to-show", () => {
    mainWindow?.show();
    if (isDev) {
      mainWindow?.webContents.openDevTools();
    }
  });

  // 让所有链接都在浏览器中打开，而不是在应用程序中打开
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: "deny" };
  });

  // 加载应用程序
  if (isDev && process.env.VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(process.env.VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(path.join(__dirname, "../dist/index.html"));
  }

  // 向渲染进程发送消息
  mainWindow.webContents.on("did-finish-load", () => {
    logger.info("📄 渲染进程加载完成 (did-finish-load)");
    mainWindow?.webContents.send("main-process-message", new Date().toLocaleString());

    // 延迟一点发送模块就绪通知，确保渲染进程完全准备好
    setTimeout(() => {
      logger.info("📤 发送模块就绪通知 (modules-ready)");
      mainWindow?.webContents.send("modules-ready");
    }, 100);
  });

  // 设置拖拽文件处理
  setupDragAndDropHandling(mainWindow);

  // 处理协议 URL（用于登录回调）
  function handleProtocolUrl(url: string) {
    console.log("🔗 收到协议 URL:", url);

    if (url.startsWith("internal://login")) {
      const urlObj = new URL(url);
      const token = urlObj.searchParams.get("token");

      if (token) {
        console.log("🎉 从协议 URL 中提取到 token:", token.substring(0, 20) + "...");

        // 存储token到主进程全局变量
        setAuthTokenInStore(token);
        console.log("💾 Token已存储到主进程");

        // 发送 token 到渲染进程
        mainWindow?.webContents.send("auth-token", token);

        // 让窗口获得焦点
        if (mainWindow) {
          mainWindow.show();
          mainWindow.focus();
        }
      }
    }
  }

  // 监听协议 URL（Windows/Linux）
  app.on("second-instance", (_, commandLine) => {
    // 应用已经在运行，有人试图启动第二个实例
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }

    // 检查命令行参数中是否有协议 URL
    const protocolUrl = commandLine.find((arg) => arg.startsWith("internal://"));
    if (protocolUrl) {
      handleProtocolUrl(protocolUrl);
    }
  });

  // 监听协议 URL（macOS）
  app.on("open-url", (event, url) => {
    event.preventDefault();
    handleProtocolUrl(url);
  });
}

app.whenReady().then(async () => {
  // 初始化日志系统
  const loggerConfig: LoggerConfig = {
    level: isDev ? "debug" : "info",
    maxSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 10,
    console: true,
    timestamp: true,
    processInfo: true,
    rotation: true,
    moduleInfo: true,
    // 日志文件存储在应用数据目录下的logs文件夹中
    logPath: path.join(app.getPath("userData"), "logs"),
  };

  initializeLogger(loggerConfig);
  initializeLoggerIPC();

  logger.info("🚀 应用启动");
  logger.info(`📂 应用版本: ${app.getVersion()}`);
  logger.info(`💻 操作系统: ${os.platform()} ${os.release()}`);
  logger.info(`📝 日志路径: ${path.join(app.getPath("userData"), "logs")}`);
  logger.info(`🔧 开发模式: ${isDev ? "是" : "否"}`);

  // 在开发环境中禁用安全警告（仅用于开发）
  if (isDev) {
    process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = "true";
  }

  // 注册自定义协议处理器
  app.setAsDefaultProtocolClient("internal");

  // 注册禁用刷新快捷键
  registerDisableRefreshShortcuts();

  // 注册开发者工具快捷键
  registerDevToolsShortcut();

  createWindow();

  // 初始化 TUS 模块
  if (mainWindow) {
    const { VITE_TUS_ENDPOINT, VITE_TUS_CHUNK_SIZE, VITE_TUS_PARALLEL_UPLOADS } = process.env;
    const MB = 1024 * 1024;
    const uploadConfig: Partial<TusUploadConfig> = {
      endpoint: VITE_TUS_ENDPOINT,
      chunkSize: Number(VITE_TUS_CHUNK_SIZE) || 5 * MB,
      retryDelays: [0, 1000, 3000, 5000],
      parallelUploads: Number(VITE_TUS_PARALLEL_UPLOADS) || 5,
    };

    const tusModule = initializeTusModule(mainWindow, uploadConfig);
    tusModuleCleanup = tusModule.cleanup;

    console.log(`📡 TUS client configured for endpoint: ${uploadConfig.endpoint}`);

    // 初始化解压缩模块
    const extractionConfig: ExtractionConfig = {
      maxConcurrent: 2,
      timeout: 5 * 60 * 1000, // 5分钟
      deleteOriginalAfterExtraction: true, // 解压完成后删除源文件
      overwriteExisting: false,
      createSubfolder: true,
      passwordPromptTimeout: 30 * 1000, // 30秒
    };

    const extractionModule = initializeExtractionModule(mainWindow, extractionConfig);
    extractionModuleCleanup = extractionModule.cleanup;

    console.log(`📦 7z 解压缩模块已配置，最大并发: ${extractionConfig.maxConcurrent}`);

    // 初始化下载模块（传递解压缩管理器）
    const { VITE_DOWNLOAD_CHUNK_SIZE, VITE_DOWNLOAD_MAX_CONCURRENT } = process.env;
    const downloadConfig: StreamDownloadConfig = {
      chunkSize: Number(VITE_DOWNLOAD_CHUNK_SIZE) || 5 * MB,
      maxConcurrent: Number(VITE_DOWNLOAD_MAX_CONCURRENT) || 10,
      retryDelays: [0, 1000, 3000, 5000],
      timeout: 30000,
      downloadDir: path.join(os.homedir(), "Downloads"),
    };

    const downloadModule = initializeDownloadModule(mainWindow, downloadConfig, extractionModule.extractionManager);
    downloadModuleCleanup = downloadModule.cleanup;

    console.log(`📥 StreamSaver 下载模块已配置，分片大小: ${(downloadConfig.chunkSize || 5 * MB) / MB}MB，最大并发: ${downloadConfig.maxConcurrent}`);

    // 初始化压缩模块
    const archiveConfig: Partial<ArchiveConfig> = {
      compressionLevel: 0, // 仅存储，不压缩
      format: "7z",
      tempDir: path.join(os.tmpdir(), "clouddrive-archives"),
      maxConcurrent: 2,
    };

    const archiveModule = initializeArchiveModule(mainWindow, archiveConfig);
    archiveModuleCleanup = archiveModule.cleanup;

    console.log(`📦 Archive 压缩模块已配置，压缩级别: ${archiveConfig.compressionLevel}，最大并发: ${archiveConfig.maxConcurrent}`);

    // 所有模块初始化完成，等待渲染进程加载完成后再通知
    console.log("✅ 所有 Electron 模块初始化完成，等待渲染进程加载完成");
  }

  app.on("activate", function () {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// 应用退出前清理资源
app.on("before-quit", () => {
  // 注销所有全局快捷键
  unregisterAllShortcuts();
});

app.on("window-all-closed", async () => {
  // 清理 TUS 模块资源
  if (tusModuleCleanup) {
    tusModuleCleanup();
  }

  // 清理下载模块资源
  if (downloadModuleCleanup) {
    downloadModuleCleanup();
  }

  // 清理解压缩模块资源
  if (extractionModuleCleanup) {
    extractionModuleCleanup();
  }

  // 清理压缩模块资源
  if (archiveModuleCleanup) {
    archiveModuleCleanup();
  }

  // 注销所有全局快捷键
  unregisterAllShortcuts();

  if (process.platform !== "darwin") {
    app.quit();
  }
});

// 拖拽文件处理的 IPC 处理器
ipcMain.handle("drag-drop-handle-files", async (_event, filePaths: string[]) => {
  try {
    console.log(`📁 主进程接收到拖拽文件: ${filePaths.length} 个`);

    // 验证文件路径并获取详细信息
    const fileInfos = [];

    for (const filePath of filePaths) {
      try {
        const stats = await fs.promises.stat(filePath);
        const isDirectory = stats.isDirectory();

        if (isDirectory) {
          // 如果是目录，递归获取所有文件
          // 使用父目录作为 basePath，这样相对路径会包含文件夹名称
          const parentDir = path.dirname(filePath);
          const dirFiles = await getAllFilesInDirectory(filePath, parentDir);
          fileInfos.push(...dirFiles);
        } else {
          // 如果是文件，直接添加
          fileInfos.push({
            path: filePath,
            name: path.basename(filePath),
            size: stats.size,
            isDirectory: false,
            relativePath: path.basename(filePath),
          });
        }
      } catch (error) {
        console.error(`处理文件 ${filePath} 时出错:`, error);
      }
    }

    console.log(`📁 处理完成，共 ${fileInfos.length} 个文件`);
    return {
      success: true,
      files: fileInfos,
    };
  } catch (error) {
    console.error("处理拖拽文件失败:", error);
    return {
      success: false,
      error: String(error),
    };
  }
});

// 递归获取目录中的所有文件
async function getAllFilesInDirectory(dirPath: string, basePath?: string): Promise<any[]> {
  const files = [];
  const baseDir = basePath || dirPath;

  try {
    const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      const relativePath = path.relative(baseDir, fullPath);

      if (entry.isDirectory()) {
        // 递归处理子目录
        const subFiles = await getAllFilesInDirectory(fullPath, baseDir);
        files.push(...subFiles);
      } else if (entry.isFile()) {
        const stats = await fs.promises.stat(fullPath);
        files.push({
          path: fullPath,
          name: entry.name,
          size: stats.size,
          isDirectory: false,
          relativePath: relativePath,
        });
      }
    }
  } catch (error) {
    console.error(`读取目录 ${dirPath} 失败:`, error);
  }

  return files;
}

// 文件操作的 IPC 处理器
ipcMain.handle("show-open-dialog", async (_event, options) => {
  if (!mainWindow) return;
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle("show-save-dialog", async (_event, options) => {
  if (!mainWindow) return;
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle("show-message-box", async (_event, options) => {
  if (!mainWindow) return;
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

// 处理应用信息
ipcMain.handle("get-app-version", () => {
  return app.getVersion();
});

ipcMain.handle("get-platform", () => {
  return process.platform;
});

// 获取文件信息
ipcMain.handle("get-file-info", async (_event, filePath: string) => {
  try {
    const stats = await fs.promises.stat(filePath);
    return {
      size: stats.size,
      mtime: stats.mtime.getTime(),
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory(),
    };
  } catch (error) {
    logger.error("获取文件信息失败:", error);
    throw error;
  }
});

// 处理清除认证状态的消息
ipcMain.on("clear-auth-state", () => {
  logger.info("🧹 收到清除认证状态请求");

  // 清除主进程中的token
  clearAuthToken();
  logger.info("🧹 已清除主进程中的token");

  // 清除默认session的所有数据
  const ses = session.defaultSession;
  ses
    .clearStorageData({
      storages: ["cookies", "localstorage", "websql"],
    })
    .then(() => {
      logger.info("✅ 已清除所有存储数据");
    })
    .catch((error) => {
      logger.error("❌ 清除存储数据失败:", error);
    });

  logger.info("🎯 认证状态已清除");
});
