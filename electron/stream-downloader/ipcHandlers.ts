import { ipcMain, dialog, app } from "electron";
import { join } from "path";
import { homedir } from "os";
import type { StreamDownloadManager } from "./downloadManager";
import type { StreamDownloadConfig, DownloadApiResponse } from "./types";

export function registerDownloadIpcHandlers(downloadManager: StreamDownloadManager) {
  // 创建下载任务
  ipcMain.handle("download-create-task", async (_event, fileName?: string, savePath?: string, metadata?: Record<string, any>): Promise<DownloadApiResponse> => {
    try {
      const taskId = await downloadManager.createDownloadTask(fileName, savePath, metadata);
      return { success: true, taskId };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 创建多个下载任务
  ipcMain.handle(
    "download-create-tasks",
    async (
      _event,
      downloads: Array<{
        fileName?: string;
        savePath?: string;
        metadata?: Record<string, any>;
      }>
    ): Promise<DownloadApiResponse> => {
      try {
        const taskIds: string[] = [];

        for (const download of downloads) {
          const taskId = await downloadManager.createDownloadTask(download.fileName, download.savePath, download.metadata);
          taskIds.push(taskId);
        }

        return {
          success: true,
          data: { taskIds, count: taskIds.length },
        };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );

  // 通过对话框选择保存位置创建下载任务
  ipcMain.handle("download-create-task-with-dialog", async (_event, defaultFileName?: string, metadata?: Record<string, any>): Promise<DownloadApiResponse> => {
    try {
      const result = await dialog.showSaveDialog({
        title: "选择下载保存位置",
        defaultPath: defaultFileName || "download",
        filters: [{ name: "所有文件", extensions: ["*"] }],
      });

      if (result.canceled || !result.filePath) {
        return { success: false, error: "用户取消了文件保存" };
      }

      const taskId = await downloadManager.createDownloadTask(defaultFileName, result.filePath, metadata);
      return { success: true, taskId };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 开始下载
  ipcMain.handle("download-start", async (_event, taskId: string): Promise<DownloadApiResponse> => {
    try {
      await downloadManager.startDownload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 暂停下载
  ipcMain.handle("download-pause", async (_event, taskId: string): Promise<DownloadApiResponse> => {
    try {
      await downloadManager.pauseDownload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 恢复下载
  ipcMain.handle("download-resume", async (_event, taskId: string): Promise<DownloadApiResponse> => {
    try {
      await downloadManager.resumeDownload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 取消下载
  ipcMain.handle("download-cancel", async (_event, taskId: string): Promise<DownloadApiResponse> => {
    try {
      await downloadManager.cancelDownload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 重试下载
  ipcMain.handle("download-retry", async (_event, taskId: string): Promise<DownloadApiResponse> => {
    try {
      await downloadManager.retryDownload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 删除任务
  ipcMain.handle("download-delete-task", async (_event, taskId: string): Promise<DownloadApiResponse> => {
    try {
      await downloadManager.deleteTask(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取所有任务
  ipcMain.handle("download-get-all-tasks", async (_event): Promise<DownloadApiResponse> => {
    try {
      const tasks = downloadManager.getAllTasks();
      return { success: true, data: tasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取指定任务
  ipcMain.handle("download-get-task", async (_event, taskId: string): Promise<DownloadApiResponse> => {
    try {
      const task = downloadManager.getTask(taskId);
      if (task) {
        return { success: true, task };
      } else {
        return { success: false, error: "任务不存在" };
      }
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取活跃任务
  ipcMain.handle("download-get-active-tasks", async (_event): Promise<DownloadApiResponse> => {
    try {
      const tasks = downloadManager.getActiveTasks();
      return { success: true, tasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 更新配置
  ipcMain.handle("download-update-config", async (_event, config: Partial<StreamDownloadConfig>): Promise<DownloadApiResponse> => {
    try {
      downloadManager.updateConfig(config);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 清理已完成的任务
  ipcMain.handle("download-clear-completed-tasks", async (_event): Promise<DownloadApiResponse> => {
    try {
      downloadManager.clearCompletedTasks();
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 批量开始下载
  ipcMain.handle("download-start-batch", async (_event, taskIds: string[]): Promise<DownloadApiResponse> => {
    try {
      const results = await Promise.allSettled(taskIds.map((taskId) => downloadManager.startDownload(taskId)));

      const failedCount = results.filter((result) => result.status === "rejected").length;

      return {
        success: true,
        data: {
          total: taskIds.length,
          succeeded: taskIds.length - failedCount,
          failed: failedCount,
        },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 批量暂停下载
  ipcMain.handle("download-pause-batch", async (_event, taskIds: string[]): Promise<DownloadApiResponse> => {
    try {
      const results = await Promise.allSettled(taskIds.map((taskId) => downloadManager.pauseDownload(taskId)));

      const failedCount = results.filter((result) => result.status === "rejected").length;

      return {
        success: true,
        data: {
          total: taskIds.length,
          succeeded: taskIds.length - failedCount,
          failed: failedCount,
        },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 批量恢复下载
  ipcMain.handle("download-resume-batch", async (_event, taskIds: string[]): Promise<DownloadApiResponse> => {
    try {
      const results = await Promise.allSettled(taskIds.map((taskId) => downloadManager.resumeDownload(taskId)));

      const failedCount = results.filter((result) => result.status === "rejected").length;

      return {
        success: true,
        data: {
          total: taskIds.length,
          succeeded: taskIds.length - failedCount,
          failed: failedCount,
        },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取下载统计信息
  ipcMain.handle("download-get-stats", async (_event): Promise<DownloadApiResponse> => {
    try {
      const allTasks = downloadManager.getAllTasks();
      const stats = {
        total: allTasks.length,
        pending: allTasks.filter((task) => task.status === "pending").length,
        downloading: allTasks.filter((task) => task.status === "downloading").length,
        paused: allTasks.filter((task) => task.status === "paused").length,
        completed: allTasks.filter((task) => ["completed", "extract-completed"].includes(task.status)).length,
        error: allTasks.filter((task) => task.status === "error").length,
        cancelled: allTasks.filter((task) => task.status === "cancelled").length,
        totalBytes: allTasks.reduce((sum, task) => sum + (task.fileSize || 0), 0),
        downloadedBytes: allTasks.reduce((sum, task) => sum + task.bytesDownloaded, 0),
      };

      return { success: true, data: stats };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取未完成的任务
  ipcMain.handle("download-get-unfinished-tasks", async (): Promise<DownloadApiResponse> => {
    try {
      const unfinishedTasks = downloadManager.getUnfinishedTasks();
      return { success: true, data: unfinishedTasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 清空所有任务
  ipcMain.handle("download-clear-all-tasks", async (): Promise<DownloadApiResponse> => {
    try {
      await downloadManager.clearAllTasks();
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 显示文件夹选择对话框
  ipcMain.handle("download-show-select-folder-dialog", async (_event, defaultPath?: string): Promise<DownloadApiResponse> => {
    try {
      const result = await dialog.showOpenDialog({
        title: "选择下载保存位置",
        defaultPath: defaultPath || getDefaultDownloadPath(),
        properties: ["openDirectory", "createDirectory"],
        buttonLabel: "选择文件夹",
      });

      if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
        return { success: false, error: "用户取消了文件夹选择" };
      }

      return {
        success: true,
        data: { path: result.filePaths[0] },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取默认下载路径
  ipcMain.handle("download-get-default-path", async (): Promise<DownloadApiResponse> => {
    try {
      const defaultPath = getDefaultDownloadPath();
      return {
        success: true,
        data: { path: defaultPath },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });
}

/**
 * 获取默认下载路径
 */
function getDefaultDownloadPath(): string {
  try {
    // 优先使用系统下载文件夹
    const downloadsPath = app.getPath("downloads");
    if (downloadsPath) {
      return downloadsPath;
    }
  } catch (error) {
    console.warn("获取系统下载文件夹失败:", error);
  }

  try {
    // 备选方案：用户文档文件夹下的Downloads
    const documentsPath = app.getPath("documents");
    return join(documentsPath, "Downloads");
  } catch (error) {
    console.warn("获取文档文件夹失败:", error);
  }

  // 最后备选方案：用户主目录下的Downloads
  return join(homedir(), "Downloads");
}
