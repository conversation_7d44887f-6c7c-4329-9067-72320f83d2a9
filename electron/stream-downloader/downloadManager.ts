import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import Store from "electron-store";
import { EventEmitter } from "events";
import type { DownloadTask, StreamDownloadConfig, DownloadStatus, DownloadStoreData } from "./types";
import { getAuthToken } from "../auth/authStore";
import type { SevenZipExtractionManager } from "../7z-extractor/extractionManager";

export class StreamDownloadManager extends EventEmitter {
  private store: Store<DownloadStoreData>;
  private tasks: Map<string, DownloadTask> = new Map();
  private config: StreamDownloadConfig;
  private activeDownloads: Map<string, AbortController> = new Map();
  private extractionManager?: SevenZipExtractionManager;

  constructor(config: StreamDownloadConfig, extractionManager?: SevenZipExtractionManager) {
    super();
    this.config = config;
    this.extractionManager = extractionManager;

    // 初始化存储
    this.store = new Store<DownloadStoreData>({
      name: "stream-downloads",
      defaults: {
        tasks: {},
        settings: {
          chunkSize: config.chunkSize || 5 * 1024 * 1024, // 默认 5MB
          maxConcurrent: config.maxConcurrent || 10,
          retryDelays: config.retryDelays || [0, 1000, 3000, 5000],
          maxRetries: config.maxRetries || 3, // 默认最大重试 3 次
          timeout: config.timeout || 30000,
          downloadDir: config.downloadDir || path.join(os.homedir(), "Downloads"),
        },
      },
    });

    // 恢复未完成的下载任务
    this.restoreUnfinishedTasks();
  }

  /**
   * 创建下载任务
   */
  async createDownloadTask(fileName?: string, savePath?: string, metadata?: Record<string, any>): Promise<string> {
    try {
      const taskId = this.generateTaskId();

      // 优先使用全局 authToken，metadata 中的 token 作为覆盖选项
      const enhancedMetadata = { ...metadata };
      const globalToken = getAuthToken();
      const finalToken = enhancedMetadata.token || globalToken;

      if (finalToken) {
        enhancedMetadata.token = finalToken;
        console.log("✅ 使用认证token:", enhancedMetadata.token === globalToken ? "全局token" : "metadata token");
      } else {
        console.warn("⚠️ 未找到有效的认证token，下载可能失败");
      }

      // 构建实际的下载URL
      const actualUrl = this.buildDownloadUrl(enhancedMetadata);

      // 跳过 HEAD 请求获取文件信息（后端不支持），直接使用传入的文件名
      const actualFileName = fileName || this.extractFileNameFromUrl(actualUrl);
      console.log(`📁 使用文件名: ${actualFileName}`);

      // 构建保存路径
      let actualSavePath: string;
      if (savePath) {
        // 如果指定了保存路径，检查是否为绝对路径
        if (path.isAbsolute(savePath)) {
          // 绝对路径直接使用
          actualSavePath = savePath;
        } else {
          // 相对路径，基于默认下载目录构建
          actualSavePath = path.join(this.config.downloadDir || path.join(os.homedir(), "Downloads"), savePath);
        }
      } else {
        // 如果没有指定保存路径，使用默认下载目录和文件名
        actualSavePath = path.join(this.config.downloadDir || path.join(os.homedir(), "Downloads"), actualFileName);
      }

      // 直接从 metadata 中获取预期的文件大小（现在是数值类型）
      const expectedFileSize = metadata?.expectedFileSize;

      // 使用前端传递的文件大小，如果没有则在下载过程中动态获取
      let finalFileSize: number | undefined;
      if (typeof expectedFileSize === "number" && expectedFileSize > 0) {
        finalFileSize = expectedFileSize;
        console.log(`📊 直接使用前端传递的文件大小: ${finalFileSize} bytes (${this.formatBytes(finalFileSize)})`);
      } else {
        finalFileSize = undefined;
        console.log(`⚠️ 无法获取文件大小，将在下载过程中动态获取`);
      }

      // 确保目录存在
      await fs.promises.mkdir(path.dirname(actualSavePath), { recursive: true });

      // 从 metadata 中提取子任务信息
      const isSubTask = metadata?.isSubTask === "true";
      const batchId = metadata?.batchId;

      const task: DownloadTask = {
        id: taskId,
        url: actualUrl,
        fileName: actualFileName,
        filePath: actualSavePath,
        fileSize: finalFileSize, // 使用最终确定的文件大小
        progress: 0,
        status: "pending",
        bytesDownloaded: 0,
        downloadSpeed: 0,
        remainingTime: 0,
        startTime: new Date(),
        metadata: { ...metadata },
        resumable: true, // 启用断点续传
        ranges: [],
        chunkSize: this.config.chunkSize || 5 * 1024 * 1024,
        totalChunks: 0,
        completedChunks: 0,
        headers: this.config.headers,
        isSubTask,
        batchId,
        // 7z文件解压后自动删除源文件（默认启用）
        deleteAfterExtraction: true,
      };

      console.log(`📄 使用简单下载模式: ${task.fileSize ? this.formatBytes(task.fileSize) : "文件大小未知，将动态获取"}`);

      this.tasks.set(taskId, task);
      this.saveTaskToStore(task);

      this.emit("task-created", taskId, task);

      return taskId;
    } catch (error) {
      throw new Error(`无法创建下载任务: ${error}`);
    }
  }

  /**
   * 开始下载
   */
  async startDownload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`下载任务不存在: ${taskId}`);
    }

    if (task.status === "downloading") {
      return; // 已在下载中
    }

    try {
      console.log(`🚀 开始下载: ${task.fileName}, 文件大小: ${task.fileSize ? this.formatBytes(task.fileSize) : "未知"}`);
      this.updateTaskStatus(taskId, "downloading");

      // 强制使用简单下载模式以确保稳定性
      await this.startSimpleDownload(taskId);
    } catch (error) {
      this.handleError(taskId, `启动下载失败: ${error}`);
    }
  }

  /**
   * 暂停下载
   */
  async pauseDownload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    const controller = this.activeDownloads.get(taskId);

    if (!task) {
      throw new Error(`下载任务不存在: ${taskId}`);
    }

    if (task.status !== "downloading") {
      return;
    }

    try {
      // 中止下载
      if (controller) {
        controller.abort();
        this.activeDownloads.delete(taskId);
      }

      this.updateTaskStatus(taskId, "paused");
      console.log(`暂停下载: ${task.fileName}, 已下载: ${task.bytesDownloaded} 字节`);
    } catch (error) {
      console.error("暂停下载失败:", error);
      this.updateTaskStatus(taskId, "paused"); // 即使出错也标记为暂停
    }
  }

  /**
   * 恢复下载
   */
  async resumeDownload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`下载任务不存在: ${taskId}`);
    }

    if (task.status !== "paused") {
      return;
    }

    await this.startDownload(taskId);
  }

  /**
   * 取消下载
   */
  async cancelDownload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    const controller = this.activeDownloads.get(taskId);

    if (!task) {
      throw new Error(`下载任务不存在: ${taskId}`);
    }

    console.log(`🚫 取消下载: ${task.fileName}`);

    try {
      // 中止下载
      if (controller) {
        controller.abort();
        this.activeDownloads.delete(taskId);
      }

      // 发送取消状态事件
      this.emit("task-status-changed", taskId, "cancelled");

      // 删除部分下载的文件
      if (fs.existsSync(task.filePath)) {
        await fs.promises.unlink(task.filePath);
        console.log(`🗑️ 已删除部分下载文件: ${task.filePath}`);
      }

      // 删除分片临时文件
      await this.cleanupTempFiles(taskId);

      // 完全删除任务（从内存和存储中移除）
      this.cleanupTask(taskId);

      console.log(`✅ 任务已取消并删除: ${task.fileName}`);
    } catch (error) {
      console.error("取消下载失败:", error);
      throw error;
    }
  }

  /**
   * 重试下载 - 增强的错误恢复机制
   */
  async retryDownload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`下载任务不存在: ${taskId}`);
    }

    try {
      // 重置任务状态
      task.status = "pending";
      task.error = undefined;
      task.startTime = new Date();

      // 简单下载模式：检查文件是否部分存在，支持断点续传
      if (fs.existsSync(task.filePath)) {
        const stats = await fs.promises.stat(task.filePath);
        if (stats.size > 0 && task.fileSize) {
          task.bytesDownloaded = stats.size;
          task.progress = Math.round((stats.size / task.fileSize) * 100);
          console.log(`检测到部分下载文件，从 ${stats.size} 字节处恢复`);
        } else {
          // 重置下载状态
          task.bytesDownloaded = 0;
          task.progress = 0;
        }
      } else {
        // 重置下载状态
        task.bytesDownloaded = 0;
        task.progress = 0;
      }

      console.log(`🔄 重试下载: ${task.fileName}, 当前进度: ${task.progress}%`);

      this.saveTaskToStore(task);
      await this.startDownload(taskId);
    } catch (error) {
      this.handleError(taskId, `重试下载失败: ${error}`);
    }
  }

  /**
   * 删除任务
   */
  async deleteTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    const controller = this.activeDownloads.get(taskId);

    if (controller && task?.status === "downloading") {
      await this.cancelDownload(taskId);
    }

    this.cleanupTask(taskId);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): DownloadTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 获取指定任务
   */
  getTask(taskId: string): DownloadTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取活跃任务
   */
  getActiveTasks(): DownloadTask[] {
    return Array.from(this.tasks.values()).filter((task) => ["pending", "downloading", "paused"].includes(task.status));
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<StreamDownloadConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // 更新存储中的设置
    const settings = this.store.get("settings");
    this.store.set("settings", {
      ...settings,
      ...newConfig,
    });
  }

  /**
   * 清理已完成的任务
   */
  clearCompletedTasks(): void {
    const completedTasks = Array.from(this.tasks.values()).filter((task) => ["completed", "error", "cancelled"].includes(task.status));

    console.log(`🧹 清空 ${completedTasks.length} 个已完成的任务`);
    completedTasks.forEach((task) => {
      this.cleanupTask(task.id);
    });
  }

  /**
   * 清空所有当前任务（包括正在进行的任务）
   */
  async clearAllTasks(): Promise<void> {
    const allTasks = Array.from(this.tasks.values());
    console.log(`🧹 清空所有 ${allTasks.length} 个任务`);

    for (const task of allTasks) {
      try {
        if (["pending", "downloading", "paused"].includes(task.status)) {
          // 取消正在进行的任务
          await this.cancelDownload(task.id);
        } else {
          // 直接删除已完成的任务
          this.cleanupTask(task.id);
        }
      } catch (error) {
        console.error(`清空任务失败: ${task.fileName}`, error);
        // 即使失败也要删除
        this.cleanupTask(task.id);
      }
    }

    console.log(`✅ 已清空所有任务`);
  }

  // 私有方法

  private generateTaskId(): string {
    return `download-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 检查是否应该记录进度里程碑
   * @param previousProgress 之前的进度
   * @param currentProgress 当前进度
   * @returns 是否应该记录
   */
  private shouldLogProgressMilestone(previousProgress: number, currentProgress: number): boolean {
    // 在0%, 20%, 40%, 60%, 80%, 100%时记录
    const milestones = [0, 20, 40, 60, 80, 100];

    for (const milestone of milestones) {
      if (previousProgress < milestone && currentProgress >= milestone) {
        return true;
      }
    }

    return false;
  }

  /**
   * 格式化字节数为可读格式
   * @param bytes 字节数
   * @returns 格式化后的字符串
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * 构建下载URL - 根据metadata参数构建完整的下载URL
   */
  private buildDownloadUrl(metadata?: Record<string, string>): string {
    // 验证必要参数
    if (!metadata || !metadata.category_id || !metadata.file_id) {
      throw new Error("无法构建下载URL：缺少必要的参数 (category_id, file_id)");
    }

    // 从环境变量或配置中获取baseURL
    const baseUrl = process.env.VITE_DOWNLOAD_ENDPOINT || "http://172.20.22.137:8000";

    // 构建基础URL
    let url = `${baseUrl}/netdisk/download?category_id=${metadata.category_id}&file_id=${metadata.file_id}`;

    // 获取认证token（优先使用metadata中的token，然后是全局token）
    const token = metadata.token || getAuthToken();

    if (token) {
      url += `&token=${encodeURIComponent(token)}`;
      const tokenSource = metadata.token ? "metadata" : "global";
      console.log(`🔗 构建下载URL（${tokenSource} token）: ${url.replace(token, token.substring(0, 20) + "...")}`);
    } else {
      console.log(`🔗 构建下载URL（无token）: ${url}`);
      throw new Error("认证失败：未找到有效的认证token，请重新登录");
    }

    return url;
  }

  private extractFileNameFromUrl(url: string): string {
    const urlPath = new URL(url).pathname;
    const fileName = path.basename(urlPath);
    return fileName || `download-${Date.now()}`;
  }

  // 分片下载相关方法已禁用以确保稳定性

  // startChunkedDownload 方法已移除，强制使用简单下载模式

  // downloadChunk 和 mergeChunks 方法已移除，强制使用简单下载模式

  private async startSimpleDownload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) return;

    const controller = new AbortController();
    this.activeDownloads.set(taskId, controller);

    try {
      // 检查是否需要断点续传
      const isResume = task.bytesDownloaded > 0;
      const headers = { ...task.headers };

      if (isResume) {
        headers["Range"] = `bytes=${task.bytesDownloaded}-`;
        console.log(`恢复下载: ${task.url}, 从字节 ${task.bytesDownloaded} 开始`);
      } else {
        console.log(`开始简单下载: ${task.url}`);
      }

      const response = await fetch(task.url, {
        headers,
        signal: controller.signal,
      });

      console.log(`下载响应状态: ${response.status} ${response.statusText}`);
      console.log("下载响应头:", Object.fromEntries(response.headers.entries()));

      // 检查响应状态：200 (完整下载) 或 206 (部分内容/断点续传)
      if (!response.ok && response.status !== 206) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 验证断点续传响应
      if (isResume && response.status !== 206) {
        console.warn(`服务器不支持断点续传，将重新开始下载`);
        // 重置下载进度，重新开始
        task.bytesDownloaded = 0;
        task.progress = 0;
        this.saveTaskToStore(task);
      }

      const totalSize = response.headers.get("content-length");
      const contentType = response.headers.get("content-type");

      console.log(`下载Content-Length: ${totalSize}`);
      console.log(`下载Content-Type: ${contentType}`);

      // 更新任务的文件大小
      if (totalSize) {
        const parsedSize = parseInt(totalSize, 10);
        if (!isNaN(parsedSize) && parsedSize > 0) {
          // 如果响应头的大小比现有大小更合理，则使用响应头的大小
          if (!task.fileSize || task.fileSize <= 0 || parsedSize > task.fileSize) {
            task.fileSize = parsedSize;
            console.log(`📊 从响应头更新文件大小: ${parsedSize}`);
          }
        }
      }

      // 如果仍然没有有效的文件大小，记录警告
      if (!task.fileSize || task.fileSize <= 0) {
        console.warn(`⚠️ 无法获取有效的文件大小，当前值: ${task.fileSize}`);
      }

      // 检查是否返回的是HTML错误页面
      if (contentType && contentType.includes("text/html")) {
        console.warn("警告：服务器返回了HTML内容而不是文件内容");

        // 读取前几个字节来确认内容
        const testResponse = await fetch(task.url, {
          headers: { ...task.headers, Range: "bytes=0-1023" },
          signal: controller.signal,
        });
        const testArrayBuffer = await testResponse.arrayBuffer();
        const testText = new TextDecoder().decode(testArrayBuffer);
        console.log("响应内容预览:", testText.substring(0, 300));

        if (testText.includes("<html") || testText.includes("<!DOCTYPE") || testText.includes("<title>")) {
          throw new Error("服务器返回HTML页面而不是文件内容，请检查下载URL和权限");
        }
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("无法获取响应流");
      }

      // 使用 Promise 来正确处理写入流
      await new Promise<void>((resolve, reject) => {
        // 如果是断点续传，使用追加模式；否则使用覆盖模式
        const writeOptions = isResume ? { flags: "a" } : {};
        const writeStream = fs.createWriteStream(task.filePath, writeOptions);
        let downloadedBytes = task.bytesDownloaded; // 从已下载的字节数开始计算

        // 处理写入流事件
        writeStream.on("error", reject);
        writeStream.on("finish", resolve);

        // 异步读取和写入数据
        const readAndWrite = async () => {
          try {
            while (true) {
              const { done, value } = await reader.read();

              if (done) {
                writeStream.end();
                break;
              }

              // 等待写入完成再继续
              await new Promise<void>((writeResolve, writeReject) => {
                writeStream.write(value, (error) => {
                  if (error) {
                    writeReject(error);
                  } else {
                    writeResolve();
                  }
                });
              });

              downloadedBytes += value.length;
              task.bytesDownloaded = downloadedBytes;

              // 计算进度
              let calculatedProgress = 0;
              if (task.fileSize && task.fileSize > 0) {
                calculatedProgress = Math.round((downloadedBytes / task.fileSize) * 100);
                // 确保进度不超过 100%
                calculatedProgress = Math.min(100, calculatedProgress);
              } else {
                // 如果没有文件大小信息，显示已下载的字节数作为进度指示
                // 这里我们可以设置一个基于下载量的估算进度
                calculatedProgress = Math.min(99, Math.floor(downloadedBytes / (1024 * 1024))); // 每MB增加1%，最多99%
              }

              // 检查是否需要记录进度里程碑（每20%记录一次）
              const previousProgress = task.progress || 0;
              task.progress = calculatedProgress;

              const shouldLogProgress = this.shouldLogProgressMilestone(previousProgress, calculatedProgress);
              if (shouldLogProgress) {
                console.log(`📊 下载进度: ${task.fileName}, ${calculatedProgress}%, 已下载: ${this.formatBytes(downloadedBytes)}`);
              }

              // 只有在进度合理的情况下才发送进度事件
              if (task.progress >= 0 && task.progress <= 100) {
                this.updateProgress(taskId);
              } else {
                console.warn(`⚠️ 进度异常，跳过发送: ${task.progress}%`);
              }
            }
          } catch (error) {
            writeStream.destroy();
            reject(error);
          }
        };

        readAndWrite();
      });

      this.handleSuccess(taskId);
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        return;
      }
      this.handleError(taskId, error instanceof Error ? error.message : String(error));
    } finally {
      this.activeDownloads.delete(taskId);
    }
  }

  private updateProgress(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    // 计算下载速度和剩余时间
    this.updateDownloadMetrics(taskId);

    this.saveTaskToStore(task);

    // 验证进度值的合理性
    const safeProgress = Math.min(100, Math.max(0, task.progress || 0));
    const reportedFileSize = task.fileSize || 0;

    // 只有在进度合理且文件大小有效时才发送事件
    if (safeProgress >= 0 && safeProgress <= 100 && reportedFileSize > 0) {
      this.emit("task-progress", taskId, safeProgress, task.bytesDownloaded, reportedFileSize);
    } else {
      console.warn(`⚠️ 跳过异常进度事件: progress=${task.progress}%, fileSize=${reportedFileSize}`);
      // 如果文件大小无效，仍然发送事件但使用 0 作为总大小
      if (safeProgress >= 0 && safeProgress <= 100) {
        this.emit("task-progress", taskId, safeProgress, task.bytesDownloaded, 0);
      }
    }
  }

  private updateDownloadMetrics(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    const currentTime = Date.now();
    const elapsedTime = currentTime - task.startTime.getTime();

    if (elapsedTime > 0) {
      // 计算下载速度 (bytes/s)
      task.downloadSpeed = Math.round(task.bytesDownloaded / (elapsedTime / 1000));

      // 计算预计剩余时间 (秒)
      if (task.fileSize) {
        const remainingBytes = task.fileSize - task.bytesDownloaded;
        task.remainingTime = task.downloadSpeed > 0 ? Math.round(remainingBytes / task.downloadSpeed) : 0;
      }
    }
  }

  private async handleSuccess(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.progress = 100;
    task.endTime = new Date();

    const duration = task.endTime.getTime() - (task.startTime?.getTime() || 0);
    const durationStr = duration > 0 ? `${Math.round(duration / 1000)}秒` : "未知";
    console.log(`✅ 下载完成: ${task.fileName}, 文件大小: ${task.fileSize ? this.formatBytes(task.fileSize) : "未知"}, 耗时: ${durationStr}`);

    // 检查是否为7z文件，如果是则自动触发解压缩
    if (this.is7zFile(task.fileName) && this.extractionManager) {
      console.log(`📦 检测到7z文件，开始自动解压缩: ${task.fileName}`);

      try {
        // 标记任务需要解压缩
        task.needsExtraction = true;
        task.extractionStatus = "pending";
        this.updateTaskStatus(taskId, "extracting");

        // 创建解压缩任务
        const extractionTaskId = await this.extractionManager.createExtractionTask(
          task.filePath,
          undefined, // 使用默认解压路径
          {
            downloadTaskId: taskId,
            deleteAfterExtraction: task.deleteAfterExtraction || false,
          }
        );

        task.extractionTaskId = extractionTaskId;
        this.saveTaskToStore(task);

        // 开始解压缩
        await this.extractionManager.startExtraction(extractionTaskId);

        // 监听解压缩事件
        this.setupExtractionEventListeners(taskId, extractionTaskId);
      } catch (error) {
        console.error(`❌ 自动解压缩失败: ${task.fileName}`, error);
        task.extractionStatus = "error";
        task.extractionError = error instanceof Error ? error.message : String(error);
        this.updateTaskStatus(taskId, "extract-error");
        this.emit("task-completed", taskId);
      }
    } else {
      // 普通文件下载完成
      this.updateTaskStatus(taskId, "completed");
      this.emit("task-completed", taskId);
    }
  }

  private handleError(taskId: string, error: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.error = error;
    task.endTime = new Date();

    // 检查是否是认证错误
    const isAuthError = error.includes("认证失败") || error.includes("401") || error.includes("403") || error.includes("Unauthorized");

    if (isAuthError) {
      console.error(`🔐 认证错误，停止重试: ${task.fileName} - ${error}`);
      this.updateTaskStatus(taskId, "error");
      this.emit("task-error", taskId, error);
      return;
    }

    // 增加重试计数
    if (!task.retryCount) task.retryCount = 0;

    // 检查是否应该自动重试
    const maxRetries = this.config.maxRetries || 3;
    const retryDelays = this.config.retryDelays || [1000, 3000, 5000];

    if (task.retryCount < maxRetries) {
      task.retryCount++;
      console.log(`📥 下载失败，将进行第 ${task.retryCount} 次重试: ${task.fileName} - ${error}`);

      // 延迟重试
      const delay = retryDelays[Math.min(task.retryCount - 1, retryDelays.length - 1)];
      setTimeout(async () => {
        try {
          await this.retryDownload(taskId);
        } catch (retryError) {
          console.error(`🔄 重试失败:`, retryError);
        }
      }, delay);

      return;
    }

    // 超过最大重试次数，标记为错误
    this.updateTaskStatus(taskId, "error");
    this.emit("task-error", taskId, error);
    console.error(`❌ 下载失败（已重试 ${task.retryCount} 次）: ${task.fileName} - ${error}`);
  }

  private updateTaskStatus(taskId: string, status: DownloadStatus): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.status = status;
    this.saveTaskToStore(task);
    this.emit("task-status-changed", taskId, status, task.error);
  }

  private cleanupTask(taskId: string): void {
    this.tasks.delete(taskId);
    this.activeDownloads.delete(taskId);
    this.removeTaskFromStore(taskId);
    this.cleanupTempFiles(taskId);
  }

  private async cleanupTempFiles(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) return;

    const tempDir = path.join(path.dirname(task.filePath), `.${task.id}-chunks`);

    try {
      if (fs.existsSync(tempDir)) {
        await fs.promises.rm(tempDir, { recursive: true, force: true });
        console.log(`已清理临时文件: ${tempDir}`);
      }
    } catch (error) {
      console.warn(`清理临时文件失败: ${tempDir}`, error);
    }
  }

  private saveTaskToStore(task: DownloadTask): void {
    const tasks = this.store.get("tasks", {}) as Record<string, DownloadTask>;
    const previousTask = tasks[task.id];
    tasks[task.id] = task;
    this.store.set("tasks", tasks);

    // 只在状态变化或首次保存时记录日志
    if (!previousTask || previousTask.status !== task.status) {
      console.log(`💾 任务状态更新: ${task.fileName}, 状态: ${task.status}, 进度: ${task.progress}%`);
    }
  }

  private removeTaskFromStore(taskId: string): void {
    const tasks = this.store.get("tasks", {}) as Record<string, DownloadTask>;
    delete tasks[taskId];
    this.store.set("tasks", tasks);
  }

  private async restoreUnfinishedTasks(): Promise<void> {
    try {
      const storedTasks = this.store.get("tasks", {}) as Record<string, DownloadTask>;
      console.log(`🔍 从存储中找到 ${Object.keys(storedTasks).length} 个任务`);

      for (const [taskId, taskData] of Object.entries(storedTasks)) {
        const task: DownloadTask = {
          ...taskData,
          startTime: new Date(taskData.startTime),
          endTime: taskData.endTime ? new Date(taskData.endTime) : undefined,
        };

        this.tasks.set(taskId, task);
        console.log(`📋 恢复任务: ${task.fileName}, 状态: ${task.status}, 进度: ${task.progress}%`);

        // 将正在下载的任务标记为暂停，等待用户手动恢复
        if (task.status === "downloading") {
          task.status = "paused";
          this.saveTaskToStore(task);
          console.log(`⏸️ 任务 ${task.fileName} 已标记为暂停，可手动恢复下载`);
        }

        // 发送任务创建事件，让前端知道有恢复的任务
        this.emit("task-created", taskId, task);
      }

      console.log(`✅ 已恢复 ${this.tasks.size} 个下载任务到内存`);
    } catch (error) {
      console.error("❌ 恢复下载任务失败:", error);
    }
  }

  /**
   * 获取所有未完成的任务（用于前端显示）
   */
  getUnfinishedTasks(): DownloadTask[] {
    return Array.from(this.tasks.values()).filter((task) => !["completed", "extract-completed", "cancelled"].includes(task.status));
  }

  /**
   * 检查文件是否为7z格式
   */
  private is7zFile(fileName: string): boolean {
    const ext = path.extname(fileName).toLowerCase();
    return ext === ".7z";
  }

  /**
   * 设置解压缩事件监听器
   */
  private setupExtractionEventListeners(downloadTaskId: string, extractionTaskId: string): void {
    if (!this.extractionManager) return;

    const task = this.tasks.get(downloadTaskId);
    if (!task) return;

    // 监听解压缩进度
    const onProgress = (taskId: string, progress: number, _extractedSize: number, _totalSize?: number) => {
      if (taskId === extractionTaskId) {
        task.extractionProgress = progress;
        this.saveTaskToStore(task);
        this.emit("task-progress", downloadTaskId, task.progress, task.bytesDownloaded, task.fileSize);
      }
    };

    // 监听解压缩状态变化
    const onStatusChanged = (taskId: string, status: string, error?: string) => {
      if (taskId === extractionTaskId) {
        task.extractionStatus = status as any;
        if (error) {
          task.extractionError = error;
        }
        this.saveTaskToStore(task);
        this.emit("task-status-changed", downloadTaskId, task.status, task.error);
      }
    };

    // 监听解压缩完成
    const onCompleted = (taskId: string, extractPath: string) => {
      if (taskId === extractionTaskId) {
        task.extractionStatus = "completed";
        task.extractPath = extractPath;
        this.updateTaskStatus(downloadTaskId, "extract-completed");
        this.emit("task-completed", downloadTaskId);

        console.log(`✅ 解压缩完成: ${task.fileName} -> ${extractPath}`);

        // 清理事件监听器
        this.cleanupExtractionEventListeners(onProgress, onStatusChanged, onCompleted, onError);
      }
    };

    // 监听解压缩错误
    const onError = (taskId: string, error: string) => {
      if (taskId === extractionTaskId) {
        task.extractionStatus = "error";
        task.extractionError = error;
        this.updateTaskStatus(downloadTaskId, "extract-error");
        this.emit("task-error", downloadTaskId, error);

        console.error(`❌ 解压缩失败: ${task.fileName} - ${error}`);

        // 清理事件监听器
        this.cleanupExtractionEventListeners(onProgress, onStatusChanged, onCompleted, onError);
      }
    };

    // 注册事件监听器
    this.extractionManager.on("task-progress", onProgress);
    this.extractionManager.on("task-status-changed", onStatusChanged);
    this.extractionManager.on("task-completed", onCompleted);
    this.extractionManager.on("task-error", onError);
  }

  /**
   * 清理解压缩事件监听器
   */
  private cleanupExtractionEventListeners(
    onProgress: (...args: any[]) => void,
    onStatusChanged: (...args: any[]) => void,
    onCompleted: (...args: any[]) => void,
    onError: (...args: any[]) => void
  ): void {
    if (!this.extractionManager) return;

    this.extractionManager.removeListener("task-progress", onProgress);
    this.extractionManager.removeListener("task-status-changed", onStatusChanged);
    this.extractionManager.removeListener("task-completed", onCompleted);
    this.extractionManager.removeListener("task-error", onError);
  }
}
