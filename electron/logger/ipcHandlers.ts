/**
 * 日志系统IPC处理器
 * 
 * 提供渲染进程访问日志系统的接口
 */

import { ipcMain } from 'electron';
import { logger, createLogger } from './index';

// 模块日志记录器缓存
const moduleLoggers = new Map<string, ReturnType<typeof createLogger>>();

/**
 * 初始化日志系统IPC处理器
 */
export function initializeLoggerIPC(): void {
  // 记录日志
  ipcMain.handle('logger:log', (_event, level: string, message: string, ...args: any[]) => {
    if (typeof logger[level] === 'function') {
      logger[level](message, ...args);
      return { success: true };
    }
    return { success: false, error: `无效的日志级别: ${level}` };
  });

  // 记录模块日志
  ipcMain.handle('logger:module-log', (_event, moduleName: string, level: string, message: string, ...args: any[]) => {
    try {
      // 获取或创建模块日志记录器
      if (!moduleLoggers.has(moduleName)) {
        moduleLoggers.set(moduleName, createLogger(moduleName));
      }
      
      const moduleLogger = moduleLoggers.get(moduleName);
      if (typeof moduleLogger[level] === 'function') {
        moduleLogger[level](message, ...args);
        return { success: true };
      }
      
      return { success: false, error: `无效的日志级别: ${level}` };
    } catch (error) {
      logger.error(`模块日志记录失败: ${error}`);
      return { success: false, error: String(error) };
    }
  });

  // 获取日志文件路径
  ipcMain.handle('logger:get-log-path', () => {
    try {
      // @ts-ignore - 访问内部属性
      const logPath = logger.transports.file.getFile().path;
      return { success: true, path: logPath };
    } catch (error) {
      logger.error(`获取日志文件路径失败: ${error}`);
      return { success: false, error: String(error) };
    }
  });

  // 清除日志文件
  ipcMain.handle('logger:clear-logs', () => {
    try {
      // @ts-ignore - 访问内部属性
      logger.transports.file.clear();
      return { success: true };
    } catch (error) {
      logger.error(`清除日志文件失败: ${error}`);
      return { success: false, error: String(error) };
    }
  });

  logger.info('📡 日志系统IPC处理器初始化完成');
}
