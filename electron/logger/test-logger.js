/**
 * 日志系统测试脚本
 * 
 * 用于验证日志文件持久化功能是否正常工作
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs');

// 模拟Electron环境
if (!app) {
  // 在Node.js环境中测试
  const { initializeLogger, logger, createLogger } = require('./index');
  
  console.log('🧪 开始测试日志系统...\n');
  
  // 测试配置
  const testConfig = {
    level: 'debug',
    maxSize: 1024 * 1024, // 1MB
    maxFiles: 3,
    console: true,
    timestamp: true,
    processInfo: true,
    rotation: true,
    moduleInfo: true,
    logPath: path.join(process.cwd(), 'test-logs')
  };
  
  // 初始化日志系统
  initializeLogger(testConfig);
  
  // 测试基本日志记录
  console.log('📝 测试基本日志记录...');
  logger.info('这是一条信息日志');
  logger.warn('这是一条警告日志');
  logger.error('这是一条错误日志');
  logger.debug('这是一条调试日志');
  
  // 测试模块日志记录
  console.log('📦 测试模块日志记录...');
  const uploadLogger = createLogger('upload');
  const downloadLogger = createLogger('download');
  
  uploadLogger.info('开始上传文件: test.txt');
  uploadLogger.debug('上传进度: 50%');
  uploadLogger.info('上传完成: test.txt');
  
  downloadLogger.info('开始下载文件: example.zip');
  downloadLogger.warn('下载速度较慢');
  downloadLogger.info('下载完成: example.zip');
  
  // 测试大量日志记录（用于测试轮转）
  console.log('🔄 测试日志轮转...');
  for (let i = 0; i < 100; i++) {
    logger.info(`批量测试日志 ${i + 1}/100`, { 
      iteration: i + 1,
      timestamp: new Date().toISOString(),
      data: 'x'.repeat(1000) // 增加日志大小
    });
  }
  
  // 检查日志文件是否创建
  setTimeout(() => {
    console.log('\n📂 检查日志文件...');
    const logDir = testConfig.logPath;
    
    if (fs.existsSync(logDir)) {
      const files = fs.readdirSync(logDir);
      console.log(`✅ 日志目录存在: ${logDir}`);
      console.log(`📄 日志文件数量: ${files.length}`);
      
      files.forEach(file => {
        const filePath = path.join(logDir, file);
        const stats = fs.statSync(filePath);
        console.log(`  - ${file}: ${stats.size} 字节`);
      });
      
      // 读取主日志文件的最后几行
      const mainLogFile = path.join(logDir, 'main.log');
      if (fs.existsSync(mainLogFile)) {
        console.log('\n📖 主日志文件最后几行:');
        const content = fs.readFileSync(mainLogFile, 'utf8');
        const lines = content.split('\n').filter(line => line.trim());
        const lastLines = lines.slice(-5);
        lastLines.forEach(line => console.log(`  ${line}`));
      }
    } else {
      console.log('❌ 日志目录不存在');
    }
    
    console.log('\n✅ 日志系统测试完成');
    
    // 清理测试文件
    if (process.argv.includes('--cleanup')) {
      console.log('🧹 清理测试文件...');
      try {
        fs.rmSync(logDir, { recursive: true, force: true });
        console.log('✅ 测试文件已清理');
      } catch (error) {
        console.error('❌ 清理测试文件失败:', error);
      }
    }
  }, 1000);
  
} else {
  console.log('此脚本应在Node.js环境中运行，不是Electron环境');
}

// 导出测试函数，供其他模块使用
module.exports = {
  testBasicLogging: () => {
    const { logger } = require('./index');
    logger.info('基本日志测试');
    logger.warn('警告日志测试');
    logger.error('错误日志测试');
    logger.debug('调试日志测试');
  },
  
  testModuleLogging: () => {
    const { createLogger } = require('./index');
    const testLogger = createLogger('test');
    testLogger.info('模块日志测试');
    testLogger.debug('模块调试日志测试');
  },
  
  testBulkLogging: (count = 50) => {
    const { logger } = require('./index');
    for (let i = 0; i < count; i++) {
      logger.info(`批量日志测试 ${i + 1}/${count}`);
    }
  }
};
