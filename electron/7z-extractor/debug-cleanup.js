/**
 * 7z解压源文件清理功能调试工具
 * 
 * 使用方法:
 * 1. 在Electron应用的开发者控制台中运行
 * 2. 或者在主进程中调用相关方法
 */

// 调试函数：检查解压任务的配置
function debugExtractionTask(taskId) {
  // 这个函数需要在Electron主进程中调用
  console.log('🔍 调试解压任务配置...');
  
  // 检查解压管理器实例
  if (typeof window !== 'undefined' && window.electronAPI?.extraction) {
    // 前端调试
    window.electronAPI.extraction.getTask(taskId).then(result => {
      if (result.success && result.task) {
        const task = result.task;
        console.log('📋 解压任务详情:');
        console.log('- 任务ID:', task.id);
        console.log('- 文件名:', task.fileName);
        console.log('- 源文件路径:', task.archivePath);
        console.log('- 解压路径:', task.extractPath);
        console.log('- 删除配置:', task.deleteAfterExtraction);
        console.log('- 当前状态:', task.status);
        console.log('- 进度:', task.progress + '%');
        
        if (task.deleteAfterExtraction) {
          console.log('✅ 源文件清理功能已启用');
        } else {
          console.log('❌ 源文件清理功能未启用');
        }
      } else {
        console.log('❌ 未找到解压任务:', taskId);
      }
    }).catch(error => {
      console.error('❌ 获取解压任务失败:', error);
    });
  } else {
    console.log('❌ 无法访问Electron API，请在Electron环境中运行');
  }
}

// 调试函数：检查下载任务的配置
function debugDownloadTask(taskId) {
  console.log('🔍 调试下载任务配置...');
  
  if (typeof window !== 'undefined' && window.electronAPI?.download) {
    window.electronAPI.download.getTask(taskId).then(result => {
      if (result.success && result.task) {
        const task = result.task;
        console.log('📋 下载任务详情:');
        console.log('- 任务ID:', task.id);
        console.log('- 文件名:', task.fileName);
        console.log('- 文件路径:', task.filePath);
        console.log('- 删除配置:', task.deleteAfterExtraction);
        console.log('- 当前状态:', task.status);
        console.log('- 需要解压:', task.needsExtraction);
        console.log('- 解压任务ID:', task.extractionTaskId);
        console.log('- 解压状态:', task.extractionStatus);
        
        if (task.deleteAfterExtraction) {
          console.log('✅ 下载任务的源文件清理功能已启用');
        } else {
          console.log('❌ 下载任务的源文件清理功能未启用');
        }
      } else {
        console.log('❌ 未找到下载任务:', taskId);
      }
    }).catch(error => {
      console.error('❌ 获取下载任务失败:', error);
    });
  } else {
    console.log('❌ 无法访问Electron API，请在Electron环境中运行');
  }
}

// 调试函数：监听解压事件
function debugExtractionEvents() {
  console.log('🔍 开始监听解压事件...');
  
  if (typeof window !== 'undefined' && window.electronAPI?.extraction) {
    // 监听解压任务状态变化
    window.electronAPI.extraction.onTaskStatusChanged((taskId, status, error) => {
      console.log(`📊 解压状态变化: ${taskId} -> ${status}`);
      if (error) {
        console.log(`❌ 错误信息: ${error}`);
      }
      
      // 如果状态是completed或extract-completed，检查任务详情
      if (status === 'completed' || status === 'extract-completed') {
        setTimeout(() => {
          debugExtractionTask(taskId);
        }, 1000);
      }
    });
    
    // 监听解压任务完成
    window.electronAPI.extraction.onTaskCompleted((taskId, extractPath) => {
      console.log(`🎉 解压完成: ${taskId} -> ${extractPath}`);
      debugExtractionTask(taskId);
    });
    
    // 监听解压任务错误
    window.electronAPI.extraction.onTaskError((taskId, error) => {
      console.log(`❌ 解压错误: ${taskId} - ${error}`);
    });
    
    console.log('✅ 解压事件监听器已设置');
  } else {
    console.log('❌ 无法访问Electron API，请在Electron环境中运行');
  }
}

// 调试函数：检查文件是否存在
function debugFileExists(filePath) {
  console.log('🔍 检查文件是否存在:', filePath);
  
  if (typeof window !== 'undefined' && window.electronAPI?.extraction) {
    // 这里需要添加一个检查文件存在的API
    console.log('⚠️ 需要添加文件存在检查API');
  } else {
    console.log('❌ 无法访问Electron API，请在Electron环境中运行');
  }
}

// 调试函数：获取所有解压任务
function debugAllExtractionTasks() {
  console.log('🔍 获取所有解压任务...');
  
  if (typeof window !== 'undefined' && window.electronAPI?.extraction) {
    window.electronAPI.extraction.getAllTasks().then(result => {
      if (result.success && result.tasks) {
        console.log(`📋 共找到 ${result.tasks.length} 个解压任务:`);
        result.tasks.forEach(task => {
          console.log(`- ${task.fileName} (${task.status}) - 删除配置: ${task.deleteAfterExtraction}`);
        });
      } else {
        console.log('❌ 获取解压任务失败');
      }
    }).catch(error => {
      console.error('❌ 获取解压任务失败:', error);
    });
  } else {
    console.log('❌ 无法访问Electron API，请在Electron环境中运行');
  }
}

// 调试函数：手动触发源文件清理测试
function debugManualCleanup(filePath, fileName) {
  console.log('🔍 手动测试源文件清理...');
  console.log('⚠️ 这是一个危险操作，会删除指定文件！');
  console.log('文件路径:', filePath);
  console.log('文件名:', fileName);
  
  // 这里需要添加手动清理的API调用
  console.log('⚠️ 需要添加手动清理测试API');
}

// 导出调试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    debugExtractionTask,
    debugDownloadTask,
    debugExtractionEvents,
    debugFileExists,
    debugAllExtractionTasks,
    debugManualCleanup
  };
}

// 如果在浏览器环境中，将函数添加到全局对象
if (typeof window !== 'undefined') {
  window.debugExtraction = {
    debugExtractionTask,
    debugDownloadTask,
    debugExtractionEvents,
    debugFileExists,
    debugAllExtractionTasks,
    debugManualCleanup
  };
  
  console.log('🔧 解压调试工具已加载，使用 window.debugExtraction 访问调试函数');
  console.log('可用函数:');
  console.log('- debugExtractionTask(taskId): 检查解压任务配置');
  console.log('- debugDownloadTask(taskId): 检查下载任务配置');
  console.log('- debugExtractionEvents(): 监听解压事件');
  console.log('- debugAllExtractionTasks(): 获取所有解压任务');
}
