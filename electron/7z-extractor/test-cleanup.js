/**
 * 7z解压源文件清理功能测试脚本
 * 
 * 使用方法:
 * node electron/7z-extractor/test-cleanup.js
 */

const fs = require('fs');
const path = require('path');
const { SevenZipExtractionManager } = require('./extractionManager');

// 测试配置
const testConfig = {
  maxConcurrent: 1,
  timeout: 30000,
  deleteOriginalAfterExtraction: true,
  overwriteExisting: true,
  createSubfolder: true,
  passwordPromptTimeout: 10000,
};

// 创建测试用的7z文件（模拟）
function createTestArchive() {
  const testDir = path.join(__dirname, 'test-data');
  const archivePath = path.join(testDir, 'test-archive.7z');
  
  // 确保测试目录存在
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }
  
  // 创建一个模拟的7z文件（实际上是文本文件，仅用于测试文件操作）
  fs.writeFileSync(archivePath, 'This is a test archive file for cleanup testing.');
  
  return archivePath;
}

// 创建测试解压目录
function createTestExtractDir(archivePath) {
  const extractDir = path.join(path.dirname(archivePath), 'extracted');
  
  if (!fs.existsSync(extractDir)) {
    fs.mkdirSync(extractDir, { recursive: true });
  }
  
  // 创建一些测试文件来模拟解压结果
  fs.writeFileSync(path.join(extractDir, 'file1.txt'), 'Test file 1');
  fs.writeFileSync(path.join(extractDir, 'file2.txt'), 'Test file 2');
  
  return extractDir;
}

// 清理测试数据
function cleanupTestData() {
  const testDir = path.join(__dirname, 'test-data');
  if (fs.existsSync(testDir)) {
    fs.rmSync(testDir, { recursive: true, force: true });
  }
}

// 主测试函数
async function runCleanupTest() {
  console.log('🧪 开始7z解压源文件清理功能测试...\n');
  
  // 清理之前的测试数据
  cleanupTestData();
  
  try {
    // 创建测试数据
    const archivePath = createTestArchive();
    const extractPath = createTestExtractDir(archivePath);
    
    console.log(`📦 测试压缩包: ${archivePath}`);
    console.log(`📁 解压目录: ${extractPath}`);
    console.log(`🗑️ 清理配置: ${testConfig.deleteOriginalAfterExtraction ? '启用' : '禁用'}\n`);
    
    // 创建解压管理器
    const manager = new SevenZipExtractionManager(testConfig);
    
    // 设置事件监听器
    manager.on('task-created', (taskId, task) => {
      console.log(`✅ 任务创建: ${task.fileName} (${taskId})`);
    });
    
    manager.on('task-status-changed', (taskId, status, error) => {
      console.log(`📊 状态变更: ${taskId} -> ${status}${error ? ` (${error})` : ''}`);
    });
    
    manager.on('task-completed', (taskId, extractPath) => {
      console.log(`🎉 任务完成: ${taskId} -> ${extractPath}`);
    });
    
    manager.on('task-error', (taskId, error) => {
      console.log(`❌ 任务错误: ${taskId} - ${error}`);
    });
    
    // 创建解压任务
    const taskId = await manager.createExtractionTask(
      archivePath,
      extractPath,
      {
        deleteAfterExtraction: true
      }
    );
    
    console.log(`📋 创建的任务ID: ${taskId}\n`);
    
    // 模拟解压完成（因为我们没有真正的7z文件，所以直接调用成功处理）
    console.log('🔄 模拟解压过程...');
    
    // 获取任务并更新状态
    const task = manager.getTask(taskId);
    if (task) {
      // 模拟解压过程
      task.status = 'extracting';
      task.progress = 50;
      console.log('⏳ 解压进行中... 50%');
      
      // 等待一秒模拟解压时间
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 手动触发成功处理（在实际使用中这会由Worker自动调用）
      console.log('🔧 触发解压完成处理...');
      await manager.handleSuccess(taskId);
    }
    
    // 检查结果
    console.log('\n📊 测试结果:');
    const finalTask = manager.getTask(taskId);
    if (finalTask) {
      console.log(`- 最终状态: ${finalTask.status}`);
      console.log(`- 解压目录存在: ${fs.existsSync(finalTask.extractPath)}`);
      console.log(`- 原始文件存在: ${fs.existsSync(finalTask.archivePath)}`);
      
      if (finalTask.status === 'extract-completed' && !fs.existsSync(finalTask.archivePath)) {
        console.log('✅ 源文件清理功能测试通过！');
      } else if (finalTask.status === 'completed' && fs.existsSync(finalTask.archivePath)) {
        console.log('⚠️ 解压完成但源文件保留（验证失败或清理失败）');
      } else {
        console.log('❌ 测试结果异常');
      }
    }
    
    // 关闭管理器
    await manager.shutdown();
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 清理测试数据
    console.log('\n🧹 清理测试数据...');
    cleanupTestData();
    console.log('✅ 测试完成！');
  }
}

// 运行测试
if (require.main === module) {
  runCleanupTest().catch(console.error);
}

module.exports = { runCleanupTest };
