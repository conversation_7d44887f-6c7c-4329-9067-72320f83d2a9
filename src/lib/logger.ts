/**
 * 渲染进程日志工具
 *
 * 简化版本：只在控制台输出，Electron主进程日志会写入文件
 * 前端日志可以通过开发者工具查看
 */

// 日志级别类型
export type LogLevel = "error" | "warn" | "info" | "debug";

// 日志上下文接口
export interface LogContext {
  module?: string;
  action?: string;
  data?: any;
}

/**
 * 简化的日志管理器类 - 只处理控制台输出
 */
class LoggerManager {
  private static instance: LoggerManager;
  private isDev = import.meta.env.DEV;
  private enabledModules = new Set<string>();

  private constructor() {
    // 默认启用重要模块的调试
    this.enabledModules.add("upload");
    this.enabledModules.add("error");
    this.enabledModules.add("auth");
    this.enabledModules.add("download");
    this.enabledModules.add("extraction");
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LoggerManager {
    if (!LoggerManager.instance) {
      LoggerManager.instance = new LoggerManager();
    }
    return LoggerManager.instance;
  }

  /**
   * 启用特定模块的调试
   */
  enable(module: string): void {
    this.enabledModules.add(module);
  }

  /**
   * 禁用特定模块的调试
   */
  disable(module: string): void {
    this.enabledModules.delete(module);
  }

  /**
   * 检查模块是否启用调试
   */
  private isEnabled(module?: string): boolean {
    // 非开发环境只记录错误和警告
    if (!this.isDev) {
      return true; // 在控制台中始终可见
    }

    // 开发环境中按模块过滤
    if (!module) return true;
    return this.enabledModules.has(module);
  }

  /**
   * 格式化日志前缀
   */
  private formatPrefix(level: LogLevel, context?: LogContext): string {
    const timestamp = new Date().toLocaleTimeString();
    const levelIcon = {
      info: "📝",
      warn: "⚠️",
      error: "❌",
      debug: "🔧",
    }[level];

    let prefix = `[${timestamp}] ${levelIcon}`;

    if (context?.module) {
      prefix += ` [${context.module.toUpperCase()}]`;
    }

    if (context?.action) {
      prefix += ` ${context.action}:`;
    }

    return prefix;
  }

  /**
   * 通用日志方法
   */
  private log(level: LogLevel, message: string, context?: LogContext): void {
    if (!this.isEnabled(context?.module)) return;

    const prefix = this.formatPrefix(level, context);
    const method = level === "warn" ? console.warn : level === "error" ? console.error : console.log;

    // 只在控制台输出
    if (context?.data) {
      method(prefix, message, context.data);
    } else {
      method(prefix, message);
    }

    // 注意：不再将渲染进程日志写入文件
    // 前端日志可以通过开发者工具查看
  }

  /**
   * 信息日志 - 用于一般操作反馈
   */
  info(message: string, context?: LogContext): void {
    this.log("info", message, context);
  }

  /**
   * 警告日志 - 用于潜在问题
   */
  warn(message: string, context?: LogContext): void {
    this.log("warn", message, context);
  }

  /**
   * 错误日志 - 用于错误和异常
   */
  error(message: string, context?: LogContext): void {
    this.log("error", message, context);
  }

  /**
   * 调试日志 - 用于详细的调试信息
   */
  debug(message: string, context?: LogContext): void {
    this.log("debug", message, context);
  }

  /**
   * 获取日志文件路径（仅在Electron环境中有效）
   */
  async getLogPath(): Promise<string | null> {
    // 渲染进程不处理日志文件，返回null
    console.warn("渲染进程不处理日志文件，请在主进程中查看日志");
    return null;
  }

  /**
   * 清除日志文件（仅在Electron环境中有效）
   */
  async clearLogs(): Promise<boolean> {
    // 渲染进程不处理日志文件，返回false
    console.warn("渲染进程不处理日志文件，请在主进程中清除日志");
    return false;
  }

  /**
   * 上传相关日志
   */
  upload = {
    start: (fileName: string, size?: number) => this.info(`开始上传: ${fileName}${size ? ` (${this.formatSize(size)})` : ""}`, { module: "upload", action: "start" }),
    progress: (fileName: string, progress: number) => this.debug(`上传进度: ${fileName} - ${progress}%`, { module: "upload", action: "progress" }),
    complete: (fileName: string) => this.info(`上传完成: ${fileName}`, { module: "upload", action: "complete" }),
    error: (fileName: string, error: string) => this.error(`上传失败: ${fileName} - ${error}`, { module: "upload", action: "error" }),
    batch: (batchName: string, fileCount: number) => this.info(`批量上传: ${batchName} (${fileCount}个文件)`, { module: "upload", action: "batch" }),
  };

  /**
   * 下载相关日志
   */
  download = {
    start: (fileName: string, size?: number) => this.info(`开始下载: ${fileName}${size ? ` (${this.formatSize(size)})` : ""}`, { module: "download", action: "start" }),
    progress: (fileName: string, progress: number) => this.debug(`下载进度: ${fileName} - ${progress}%`, { module: "download", action: "progress" }),
    complete: (fileName: string) => this.info(`下载完成: ${fileName}`, { module: "download", action: "complete" }),
    error: (fileName: string, error: string) => this.error(`下载失败: ${fileName} - ${error}`, { module: "download", action: "error" }),
    batch: (batchName: string, fileCount: number) => this.info(`批量下载: ${batchName} (${fileCount}个文件)`, { module: "download", action: "batch" }),
  };

  /**
   * 任务管理相关日志
   */
  task = {
    create: (taskId: string, fileName: string) => this.debug(`任务创建: ${fileName} (${taskId.substring(0, 8)}...)`, { module: "task", action: "create" }),
    status: (fileName: string, from: string, to: string) => this.debug(`任务状态变化: ${fileName} - ${from} -> ${to}`, { module: "task", action: "status" }),
    remove: (fileName: string, status: string) => this.info(`任务移除: ${fileName} (${status})`, { module: "task", action: "remove" }),
  };

  /**
   * 格式化文件大小
   */
  private formatSize(bytes: number): string {
    const units = ["B", "KB", "MB", "GB"];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
}

// 导出单例实例
export const logger = LoggerManager.getInstance();

// 兼容性方法 - 逐步替换现有的 console.log
export const log = {
  info: (message: string, data?: any) => logger.info(message, { data }),
  warn: (message: string, data?: any) => logger.warn(message, { data }),
  error: (message: string, data?: any) => logger.error(message, { data }),
  debug: (message: string, data?: any) => logger.debug(message, { data }),
};
