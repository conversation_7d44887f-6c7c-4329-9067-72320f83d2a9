/**
 * 渲染进程日志工具
 * 
 * 提供在渲染进程中使用日志系统的接口
 */

// 日志级别类型
export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

// 日志上下文接口
export interface LogContext {
  module?: string;
  action?: string;
  data?: any;
}

/**
 * 日志管理器类
 */
class LoggerManager {
  private static instance: LoggerManager;
  private isDev = import.meta.env.DEV;
  private enabledModules = new Set<string>();
  private isElectron = !!window.electronAPI;

  private constructor() {
    // 默认启用重要模块的调试
    this.enabledModules.add('upload');
    this.enabledModules.add('error');
    this.enabledModules.add('auth');
    this.enabledModules.add('download');
    this.enabledModules.add('extraction');
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LoggerManager {
    if (!LoggerManager.instance) {
      LoggerManager.instance = new LoggerManager();
    }
    return LoggerManager.instance;
  }

  /**
   * 启用特定模块的调试
   */
  enable(module: string): void {
    this.enabledModules.add(module);
  }

  /**
   * 禁用特定模块的调试
   */
  disable(module: string): void {
    this.enabledModules.delete(module);
  }

  /**
   * 检查模块是否启用调试
   */
  private isEnabled(module?: string): boolean {
    if (!module) return true;
    return this.enabledModules.has(module);
  }

  /**
   * 格式化日志前缀
   */
  private formatPrefix(level: LogLevel, context?: LogContext): string {
    const timestamp = new Date().toLocaleTimeString();
    const levelIcon = {
      info: '📝',
      warn: '⚠️',
      error: '❌',
      debug: '🔧',
    }[level];

    let prefix = `[${timestamp}] ${levelIcon}`;

    if (context?.module) {
      prefix += ` [${context.module.toUpperCase()}]`;
    }

    if (context?.action) {
      prefix += ` ${context.action}:`;
    }

    return prefix;
  }

  /**
   * 通用日志方法
   */
  private log(level: LogLevel, message: string, context?: LogContext): void {
    if (!this.isEnabled(context?.module)) return;

    const prefix = this.formatPrefix(level, context);
    const method = level === 'warn' ? console.warn : level === 'error' ? console.error : console.log;

    // 在控制台输出
    if (context?.data) {
      method(prefix, message, context.data);
    } else {
      method(prefix, message);
    }

    // 如果在Electron环境中，同时写入日志文件
    if (this.isElectron && window.electronAPI?.logger) {
      if (context?.module) {
        // 使用模块日志
        window.electronAPI.logger.moduleLog(context.module, level, message, context?.data).catch((error: any) => {
          console.error('写入模块日志失败:', error);
        });
      } else {
        // 使用普通日志
        window.electronAPI.logger.log(level, message, context?.data).catch((error: any) => {
          console.error('写入日志失败:', error);
        });
      }
    }
  }

  /**
   * 信息日志 - 用于一般操作反馈
   */
  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
  }

  /**
   * 警告日志 - 用于潜在问题
   */
  warn(message: string, context?: LogContext): void {
    this.log('warn', message, context);
  }

  /**
   * 错误日志 - 用于错误和异常
   */
  error(message: string, context?: LogContext): void {
    this.log('error', message, context);
  }

  /**
   * 调试日志 - 用于详细的调试信息
   */
  debug(message: string, context?: LogContext): void {
    this.log('debug', message, context);
  }

  /**
   * 获取日志文件路径
   */
  async getLogPath(): Promise<string | null> {
    if (this.isElectron && window.electronAPI?.logger) {
      try {
        const result = await window.electronAPI.logger.getLogPath();
        if (result.success) {
          return result.path;
        }
      } catch (error) {
        console.error('获取日志文件路径失败:', error);
      }
    }
    return null;
  }

  /**
   * 清除日志文件
   */
  async clearLogs(): Promise<boolean> {
    if (this.isElectron && window.electronAPI?.logger) {
      try {
        const result = await window.electronAPI.logger.clearLogs();
        return result.success;
      } catch (error) {
        console.error('清除日志文件失败:', error);
      }
    }
    return false;
  }

  /**
   * 上传相关日志
   */
  upload = {
    start: (fileName: string, size?: number) => this.info(`开始上传: ${fileName}${size ? ` (${this.formatSize(size)})` : ''}`, { module: 'upload', action: 'start' }),
    progress: (fileName: string, progress: number) => this.debug(`上传进度: ${fileName} - ${progress}%`, { module: 'upload', action: 'progress' }),
    complete: (fileName: string) => this.info(`上传完成: ${fileName}`, { module: 'upload', action: 'complete' }),
    error: (fileName: string, error: string) => this.error(`上传失败: ${fileName} - ${error}`, { module: 'upload', action: 'error' }),
    batch: (batchName: string, fileCount: number) => this.info(`批量上传: ${batchName} (${fileCount}个文件)`, { module: 'upload', action: 'batch' }),
  };

  /**
   * 下载相关日志
   */
  download = {
    start: (fileName: string, size?: number) => this.info(`开始下载: ${fileName}${size ? ` (${this.formatSize(size)})` : ''}`, { module: 'download', action: 'start' }),
    progress: (fileName: string, progress: number) => this.debug(`下载进度: ${fileName} - ${progress}%`, { module: 'download', action: 'progress' }),
    complete: (fileName: string) => this.info(`下载完成: ${fileName}`, { module: 'download', action: 'complete' }),
    error: (fileName: string, error: string) => this.error(`下载失败: ${fileName} - ${error}`, { module: 'download', action: 'error' }),
    batch: (batchName: string, fileCount: number) => this.info(`批量下载: ${batchName} (${fileCount}个文件)`, { module: 'download', action: 'batch' }),
  };

  /**
   * 任务管理相关日志
   */
  task = {
    create: (taskId: string, fileName: string) => this.debug(`任务创建: ${fileName} (${taskId.substring(0, 8)}...)`, { module: 'task', action: 'create' }),
    status: (fileName: string, from: string, to: string) => this.debug(`任务状态变化: ${fileName} - ${from} -> ${to}`, { module: 'task', action: 'status' }),
    remove: (fileName: string, status: string) => this.info(`任务移除: ${fileName} (${status})`, { module: 'task', action: 'remove' }),
  };

  /**
   * 格式化文件大小
   */
  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
}

// 导出单例实例
export const logger = LoggerManager.getInstance();

// 兼容性方法 - 逐步替换现有的 console.log
export const log = {
  info: (message: string, data?: any) => logger.info(message, { data }),
  warn: (message: string, data?: any) => logger.warn(message, { data }),
  error: (message: string, data?: any) => logger.error(message, { data }),
  debug: (message: string, data?: any) => logger.debug(message, { data }),
};
