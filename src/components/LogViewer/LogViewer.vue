<template>
  <div class="log-viewer">
    <div class="log-header">
      <h3 class="text-lg font-semibold">应用日志</h3>
      <div class="flex gap-2">
        <button
          @click="refreshLogPath"
          class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          :disabled="loading"
        >
          刷新路径
        </button>
        <button
          @click="openLogFolder"
          class="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
          :disabled="!logPath"
        >
          打开日志文件夹
        </button>
        <button
          @click="clearLogs"
          class="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
          :disabled="loading"
        >
          清除日志
        </button>
      </div>
    </div>

    <div class="log-content">
      <div v-if="loading" class="text-center py-4">
        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <span class="ml-2">加载中...</span>
      </div>

      <div v-else-if="error" class="text-red-500 p-4 bg-red-50 rounded">
        <p class="font-semibold">错误</p>
        <p>{{ error }}</p>
      </div>

      <div v-else class="space-y-4">
        <div class="log-info">
          <h4 class="font-semibold mb-2">日志信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium">日志文件路径:</span>
              <p class="text-gray-600 break-all">{{ logPath || '未知' }}</p>
            </div>
            <div>
              <span class="font-medium">日志状态:</span>
              <p class="text-gray-600">{{ logPath ? '已启用' : '未启用' }}</p>
            </div>
          </div>
        </div>

        <div class="log-actions">
          <h4 class="font-semibold mb-2">日志操作</h4>
          <div class="space-y-2">
            <button
              @click="testLogging"
              class="w-full px-4 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              测试日志记录
            </button>
            <p class="text-xs text-gray-500">
              点击此按钮将记录测试日志到文件中，用于验证日志系统是否正常工作。
            </p>
          </div>
        </div>

        <div class="log-usage">
          <h4 class="font-semibold mb-2">使用说明</h4>
          <div class="text-sm text-gray-600 space-y-2">
            <p>• 日志文件会自动保存在应用数据目录的 logs 文件夹中</p>
            <p>• 支持日志轮转，单个文件最大 10MB，保留最近 10 个文件</p>
            <p>• 开发环境记录 debug 级别，生产环境记录 info 级别</p>
            <p>• 日志包含时间戳、级别、进程信息和模块信息</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { logger } from '@/lib/logger'

// 响应式数据
const logPath = ref<string | null>(null)
const loading = ref(false)
const error = ref<string | null>(null)

// 刷新日志路径
const refreshLogPath = async () => {
  loading.value = true
  error.value = null
  
  try {
    const path = await logger.getLogPath()
    logPath.value = path
  } catch (err) {
    error.value = err instanceof Error ? err.message : '获取日志路径失败'
  } finally {
    loading.value = false
  }
}

// 打开日志文件夹
const openLogFolder = async () => {
  if (!logPath.value) return
  
  try {
    // 获取日志文件夹路径（去掉文件名）
    const folderPath = logPath.value.substring(0, logPath.value.lastIndexOf('/'))
    
    if (window.electronAPI?.showOpenDialog) {
      // 在Electron中，我们可以使用shell.openPath
      // 这里我们通过显示文件夹选择对话框来间接打开文件夹
      await window.electronAPI.showOpenDialog({
        defaultPath: folderPath,
        properties: ['openDirectory']
      })
    } else {
      // 在浏览器中，复制路径到剪贴板
      await navigator.clipboard.writeText(folderPath)
      alert(`日志文件夹路径已复制到剪贴板:\n${folderPath}`)
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '打开日志文件夹失败'
  }
}

// 清除日志
const clearLogs = async () => {
  if (!confirm('确定要清除所有日志文件吗？此操作不可撤销。')) {
    return
  }
  
  loading.value = true
  error.value = null
  
  try {
    const success = await logger.clearLogs()
    if (success) {
      alert('日志文件已清除')
    } else {
      throw new Error('清除日志失败')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '清除日志失败'
  } finally {
    loading.value = false
  }
}

// 测试日志记录
const testLogging = () => {
  const timestamp = new Date().toLocaleString()
  
  logger.info(`测试信息日志 - ${timestamp}`, { module: 'test', action: 'info' })
  logger.warn(`测试警告日志 - ${timestamp}`, { module: 'test', action: 'warn' })
  logger.error(`测试错误日志 - ${timestamp}`, { module: 'test', action: 'error' })
  logger.debug(`测试调试日志 - ${timestamp}`, { module: 'test', action: 'debug' })
  
  alert('测试日志已记录，请检查日志文件')
}

// 组件挂载时获取日志路径
onMounted(() => {
  refreshLogPath()
})
</script>

<style scoped>
.log-viewer {
  @apply max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg;
}

.log-header {
  @apply flex justify-between items-center mb-6 pb-4 border-b;
}

.log-content {
  @apply space-y-6;
}

.log-info,
.log-actions,
.log-usage {
  @apply p-4 bg-gray-50 rounded-lg;
}

button:disabled {
  @apply opacity-50 cursor-not-allowed;
}
</style>
