# 7z解压源文件自动删除功能故障排查指南

## 问题现象
7z压缩包解压成功后，原始压缩包文件没有被自动删除。

## 排查步骤

### 1. 检查全局配置

首先确认解压管理器的全局配置是否正确：

**位置**: `electron/main.ts` 第308行
```typescript
const extractionConfig: ExtractionConfig = {
  maxConcurrent: 2,
  timeout: 5 * 60 * 1000, // 5分钟
  deleteOriginalAfterExtraction: true, // ✅ 确保这里是 true
  overwriteExisting: false,
  createSubfolder: true,
  passwordPromptTimeout: 30 * 1000, // 30秒
};
```

### 2. 检查下载任务配置

**位置**: `electron/stream-downloader/downloadManager.ts` 第126行
```typescript
// 7z文件解压后自动删除源文件（默认启用）
deleteAfterExtraction: true, // ✅ 确保这里是 true
```

### 3. 检查解压任务创建

**位置**: `electron/stream-downloader/downloadManager.ts` 第705行
```typescript
deleteAfterExtraction: task.deleteAfterExtraction || false,
```
应该传递 `task.deleteAfterExtraction`，如果下载任务的 `deleteAfterExtraction` 为 `true`，则解压任务也会启用删除功能。

### 4. 使用调试工具

在浏览器开发者控制台中运行以下代码：

```javascript
// 加载调试工具
const script = document.createElement('script');
script.src = 'file:///path/to/electron/7z-extractor/debug-cleanup.js';
document.head.appendChild(script);

// 监听解压事件
window.debugExtraction.debugExtractionEvents();

// 检查所有解压任务
window.debugExtraction.debugAllExtractionTasks();
```

### 5. 检查控制台日志

在解压过程中，查看控制台输出，寻找以下关键日志：

#### 正常流程日志：
```
✅ 解压缩完成: example.7z, 耗时: 15秒
✅ 解压结果验证通过: example.7z -> /path/to/extract (25 个文件/目录)
🗑️ 已删除原始压缩包: example.7z (/path/to/example.7z)
```

#### 验证失败日志：
```
✅ 解压缩完成: example.7z, 耗时: 15秒
❌ 解压目录为空: /path/to/extract
⚠️ 解压验证失败，保留原始文件: example.7z
```

#### 删除失败日志：
```
✅ 解压缩完成: example.7z, 耗时: 15秒
✅ 解压结果验证通过: example.7z -> /path/to/extract (25 个文件/目录)
❌ 删除原始压缩包失败: example.7z (/path/to/example.7z) Error: EACCES: permission denied
```

### 6. 检查任务状态

解压完成后，检查任务的最终状态：

- **`extract-completed`**: 解压成功且验证通过，源文件应已删除
- **`completed`**: 解压成功但验证失败，源文件保留

### 7. 常见问题及解决方案

#### 问题1: deleteAfterExtraction配置为false
**症状**: 解压成功但源文件未删除，无相关日志
**解决**: 检查步骤1和2中的配置

#### 问题2: 解压验证失败
**症状**: 看到"解压验证失败"日志
**可能原因**:
- 解压目录不存在
- 解压目录为空
- 解压目录权限问题

**解决**: 检查解压目录和权限设置

#### 问题3: 删除操作失败
**症状**: 看到"删除原始压缩包失败"日志
**可能原因**:
- 文件权限问题
- 文件被其他进程占用
- 磁盘空间不足

**解决**: 检查文件权限和系统资源

#### 问题4: 解压任务状态异常
**症状**: 任务状态不是expected状态
**解决**: 检查Worker进程和事件监听器

### 8. 手动验证

可以手动创建解压任务来测试功能：

```javascript
// 在开发者控制台中运行
window.electronAPI.extraction.createTask(
  '/path/to/test.7z',
  '/path/to/extract',
  {
    deleteAfterExtraction: true
  }
).then(result => {
  if (result.success) {
    console.log('解压任务创建成功:', result.taskId);
    // 开始解压
    return window.electronAPI.extraction.startExtraction(result.taskId);
  }
}).then(() => {
  console.log('解压任务已启动');
}).catch(error => {
  console.error('测试失败:', error);
});
```

### 9. 检查文件系统

确认以下几点：
- 源压缩包文件确实存在
- 解压目录有写权限
- 磁盘空间充足
- 没有防病毒软件阻止文件删除

### 10. 重启应用

如果以上步骤都无法解决问题，尝试：
1. 重启Electron应用
2. 清除应用数据（electron-store）
3. 重新下载和解压测试文件

## 预防措施

1. **定期检查日志**: 监控解压和删除操作的日志输出
2. **测试环境验证**: 在测试环境中验证功能正常工作
3. **权限管理**: 确保应用有足够的文件系统权限
4. **错误处理**: 实现完善的错误处理和用户通知机制

## 联系支持

如果问题仍然存在，请提供以下信息：
- 完整的控制台日志
- 操作系统版本
- 文件路径和权限信息
- 复现步骤
