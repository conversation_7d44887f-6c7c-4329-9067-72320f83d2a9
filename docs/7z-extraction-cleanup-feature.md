# 7z文件解压后源压缩包自动清理功能

## 功能概述

本功能实现了7z压缩包下载并成功解压完成后，自动删除原始压缩包文件的能力。该功能确保只有在解压操作完全成功且通过验证后才会执行删除操作。

## 核心特性

### 1. 解压结果验证
- 检查解压目标目录是否存在
- 验证解压目录是否包含文件
- 确认解压目录的可读性
- 只有通过所有验证的解压任务才会触发源文件清理

### 2. 新增任务状态
- 新增 `extract-completed` 状态，表示解压完成且通过验证
- 原有 `completed` 状态保留，用于解压完成但验证失败的情况

### 3. 安全的文件删除
- 删除前再次确认源文件存在
- 包含完整的错误处理机制
- 删除失败时记录日志但不影响解压任务状态
- 静默处理，无需用户交互

## 状态流转

```
pending -> extracting -> completed/extract-completed
                    \-> error
                    \-> cancelled
```

- `extract-completed`: 解压成功且验证通过，源文件已清理（如果配置启用）
- `completed`: 解压成功但验证失败，源文件保留

## 配置选项

在创建解压任务时，可以通过以下选项控制源文件清理行为：

```typescript
// 全局配置
const config: ExtractionConfig = {
  deleteOriginalAfterExtraction: true, // 默认false
  // ... 其他配置
};

// 单个任务配置
await extractionManager.createExtractionTask(
  archivePath,
  extractPath,
  {
    deleteAfterExtraction: true, // 覆盖全局配置
    // ... 其他选项
  }
);
```

## 错误处理

### 解压验证失败
- 保留原始压缩包文件
- 任务状态设置为 `completed`
- 记录警告日志

### 删除操作失败
- 记录错误日志但不抛出异常
- 不影响解压任务的最终状态
- 任务状态仍为 `extract-completed`

## 日志输出示例

### 成功场景
```
✅ 解压缩完成: example.7z, 耗时: 15秒
✅ 解压结果验证通过: example.7z -> /path/to/extract (25 个文件/目录)
🗑️ 已删除原始压缩包: example.7z (/path/to/example.7z)
```

### 验证失败场景
```
✅ 解压缩完成: example.7z, 耗时: 15秒
❌ 解压目录为空: /path/to/extract
⚠️ 解压验证失败，保留原始文件: example.7z
```

### 删除失败场景
```
✅ 解压缩完成: example.7z, 耗时: 15秒
✅ 解压结果验证通过: example.7z -> /path/to/extract (25 个文件/目录)
❌ 删除原始压缩包失败: example.7z (/path/to/example.7z) Error: EACCES: permission denied
```

## API变更

### 新增状态类型
```typescript
export type ExtractionStatus = 
  | "pending" 
  | "extracting" 
  | "paused" 
  | "completed" 
  | "extract-completed"  // 新增
  | "error" 
  | "cancelled";
```

### 方法变更
- `handleSuccess()` 方法现在是异步的
- 新增 `validateExtractionResult()` 私有方法
- 新增 `cleanupOriginalFile()` 私有方法
- 更新了所有涉及完成状态检查的方法

## 使用建议

1. **生产环境**: 建议启用此功能以节省存储空间
2. **开发环境**: 可以禁用此功能以便调试和验证
3. **监控**: 关注删除失败的日志，可能需要手动清理
4. **备份**: 对于重要文件，建议在启用此功能前确保有适当的备份策略

## 兼容性

- 向后兼容现有的解压功能
- 不影响现有的任务管理和状态查询
- 现有的事件监听器继续正常工作
